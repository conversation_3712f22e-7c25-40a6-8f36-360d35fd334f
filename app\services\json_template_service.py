"""
JSON Template Processing Service
Xử lý slide generation với JSON template từ frontend thay vì Google Slides
"""

import logging
import re
from typing import Dict, List, Any, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import get_textbook_retrieval_service

logger = logging.getLogger(__name__)


class JsonTemplateService:
    """Service xử lý JSON template từ frontend"""
    
    def __init__(self):
        self.llm_service = get_llm_service()
        self.textbook_service = get_textbook_retrieval_service()

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        return (
            self.llm_service and self.llm_service.is_available() and
            self.textbook_service is not None
        )
    
    async def process_json_template(
        self,
        lesson_id: str,
        template_json: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Xử lý JSON template với nội dung bài học
        
        Args:
            lesson_id: ID của bài học
            template_json: JSON template từ frontend
            config_prompt: Prompt cấu hình tùy chỉnh
            
        Returns:
            Dict chứa template đã được xử lý
        """
        try:
            logger.info(f"🔄 Processing JSON template for lesson: {lesson_id}")
            
            # Bước 1: Lấy nội dung bài học
            lesson_content = await self._get_lesson_content(lesson_id)
            if not lesson_content["success"]:
                return lesson_content
                
            # Bước 2: Phân tích template và detect placeholders
            analyzed_template = self._analyze_json_template(template_json)
            logger.info(f"📊 Analyzed template: {len(analyzed_template['slides'])} slides")
            
            # Bước 3: Sinh nội dung với LLM
            presentation_content = await self._generate_presentation_content(
                lesson_content["content"],
                analyzed_template,
                config_prompt
            )
            if not presentation_content["success"]:
                return presentation_content
                
            # Bước 4: Map nội dung vào template
            processed_template = await self._map_content_to_json_template(
                presentation_content["content"],
                template_json,
                analyzed_template
            )
            
            return {
                "success": True,
                "lesson_id": lesson_id,
                "processed_template": processed_template,
                "slides_created": len(processed_template.get("slides", [])),
                "presentation_content": presentation_content["content"]  # For debugging
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing JSON template: {e}")
            return {
                "success": False,
                "error": f"Failed to process JSON template: {str(e)}"
            }
    
    async def _get_lesson_content(self, lesson_id: str) -> Dict[str, Any]:
        """Lấy nội dung bài học từ TextbookRetrievalService"""
        try:
            logger.info(f"📚 Getting lesson content for: {lesson_id}")

            # Sử dụng TextbookRetrievalService để lấy lesson content
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)

            logger.info(f"🔍 Lesson result keys: {list(lesson_result.keys())}")

            # Extract lesson content từ result
            lesson_content = lesson_result.get("lesson_content", "")

            if not lesson_content or not lesson_content.strip():
                logger.error(f"❌ No lesson content found for lesson_id: {lesson_id}")
                return {
                    "success": False,
                    "error": f"Empty lesson content for lesson_id: {lesson_id}"
                }

            logger.info(f"✅ Retrieved lesson content: {len(lesson_content)} characters")
            logger.info(f"📋 Additional info - Book ID: {lesson_result.get('book_id')}, Total chunks: {lesson_result.get('total_chunks')}")

            return {
                "success": True,
                "content": lesson_content.strip(),
                "book_id": lesson_result.get("book_id"),
                "total_chunks": lesson_result.get("total_chunks"),
                "content_length": lesson_result.get("content_length")
            }

        except Exception as e:
            logger.error(f"❌ Error getting lesson content: {e}")
            return {
                "success": False,
                "error": f"Failed to get lesson content: {str(e)}"
            }
    
    def _analyze_json_template(self, template_json: Dict[str, Any]) -> Dict[str, Any]:
        """Phân tích JSON template và detect placeholders (theo logic cũ)"""
        try:
            logger.info("🔍 Analyzing JSON template structure...")

            slides = template_json.get("slides", [])
            analyzed_slides = []

            # Placeholder patterns để detect
            placeholder_patterns = {
                "LessonName": r"LessonName\s+(\d+)",
                "LessonDescription": r"LessonDescription\s+(\d+)",
                "CreatedDate": r"CreatedDate\s+(\d+)",
                "TitleName": r"TitleName\s+(\d+)",
                "TitleContent": r"TitleContent\s+(\d+)",
                "SubtitleName": r"SubtitleName\s+(\d+)",
                "SubtitleContent": r"SubtitleContent\s+(\d+)",
                "ImageName": r"ImageName\s+(\d+)",
                "ImageContent": r"ImageContent\s+(\d+)"
            }

            for slide in slides:
                analyzed_elements = []
                placeholder_counts = {}

                # Phân tích elements
                for element in slide.get("elements", []):
                    text = element.get("text", "").strip()

                    # Detect placeholder type từ text
                    placeholder_result = self._detect_placeholder_type_from_text(text, placeholder_patterns)

                    if placeholder_result:  # Chỉ xử lý nếu detect được placeholder
                        placeholder_type, max_length = placeholder_result

                        logger.info(f"✅ Found placeholder: {placeholder_type} <{max_length}>")

                        # Đếm số lượng placeholder types
                        placeholder_counts[placeholder_type] = placeholder_counts.get(placeholder_type, 0) + 1

                        # Tạo analyzed element với thông tin đầy đủ
                        analyzed_element = {
                            "objectId": element.get("id"),
                            "text": None,  # LLM sẽ insert nội dung sau
                            "Type": placeholder_type,
                            "max_length": max_length,
                            "original_element": element  # Giữ thông tin gốc để mapping
                        }

                        analyzed_elements.append(analyzed_element)
                    else:
                        # Bỏ qua text không phải placeholder format
                        logger.info(f"❌ Skipping non-placeholder text: '{text}'")
                        continue

                # Tạo description cho slide dựa trên placeholder counts (như luồng cũ)
                description = self._generate_slide_description(placeholder_counts)

                analyzed_slide = {
                    "slideId": slide.get("id"),
                    "description": description,
                    "elements": analyzed_elements,
                    "placeholder_counts": placeholder_counts,  # For logic selection
                    "original_slide": slide  # Giữ thông tin gốc
                }

                analyzed_slides.append(analyzed_slide)

            result = {
                "slides": analyzed_slides,
                "total_slides": len(analyzed_slides),
                "slideFormat": template_json.get("slideFormat", "16:9"),
                "version": template_json.get("version", "1.0")
            }

            logger.info(f"✅ Template analysis complete: {len(analyzed_slides)} slides analyzed")
            return result

        except Exception as e:
            logger.error(f"❌ Error analyzing JSON template: {e}")
            raise
    
    async def _generate_presentation_content(
        self,
        lesson_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """Sinh nội dung presentation với LLM"""
        try:
            logger.info("🤖 Generating presentation content with LLM...")
            
            # Tạo prompt cho LLM
            prompt = self._create_llm_prompt(lesson_content, analyzed_template, config_prompt)
            
            # Gọi LLM
            llm_response = await self.llm_service.generate_content(
                prompt=prompt,
                max_tokens=20000,
                temperature=0.1
            )
            
            if not llm_response.get("success", False):
                return {
                    "success": False,
                    "error": f"LLM generation failed: {llm_response.get('error', 'Unknown error')}"
                }

            content = llm_response.get("text", "")  # LLMService trả về "text" chứ không phải "content"
            logger.info(f"✅ LLM content generated: {len(content)} characters")

            # Debug: Log first 500 chars of LLM content
            logger.info(f"🔍 LLM content preview: {content[:500]}...")

            return {
                "success": True,
                "content": content
            }
            
        except Exception as e:
            logger.error(f"❌ Error generating presentation content: {e}")
            return {
                "success": False,
                "error": f"Failed to generate content: {str(e)}"
            }
    
    def _create_llm_prompt(
        self,
        lesson_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> str:
        """Tạo prompt cho LLM theo format của luồng cũ"""

        # Tạo slide descriptions từ analyzed template
        slide_descriptions = []
        for i, slide in enumerate(analyzed_template["slides"], 1):
            description = slide.get("description", "Slide không xác định")
            slide_descriptions.append(f"SLIDE {i}: {description}")

        prompt = f"""Bạn là chuyên gia tạo nội dung slide giáo dục. Hãy tạo nội dung cho presentation dựa trên bài học sau:

LESSON CONTENT:
{lesson_content}

TEMPLATE STRUCTURE:
{chr(10).join(slide_descriptions)}

PLACEHOLDER TYPES VÀ Ý NGHĨA:
- LessonName: Tên bài học ngắn gọn
- LessonDescription: Mô tả tổng quan về bài học
- CreatedDate: Ngày tạo slide (định dạng dd/mm/yyyy)
- TitleName: Tiêu đề chính của slide (chỉ là tiêu đề)
- TitleContent: Nội dung chi tiết thuộc mục lớn (nhóm tất cả nội dung chung)
- SubtitleName: Tiêu đề phụ bên trong mục lớn (chỉ là tiêu đề con)
- SubtitleContent: Nội dung chi tiết thuộc mục nhỏ (nhóm tất cả nội dung con chung)
- ImageName: Tên/mô tả hình ảnh
- ImageContent: Mô tả chi tiết nội dung hình ảnh

INSTRUCTIONS:
1. Tạo nội dung cho từng placeholder theo format: #*(PlaceholderType)*# [nội dung]
2. Sau mỗi slide, thêm summary line: === SLIDE X SUMMARY === và Placeholders: [danh sách placeholders]
3. Nội dung phải liên quan trực tiếp đến bài học
4. TitleContent và SubtitleContent phải nhóm tất cả nội dung liên quan
5. Sử dụng ngôn ngữ phù hợp với học sinh

VÍ DỤ FORMAT:
SLIDE 1 - GIỚI THIỆU:
Hàm số bậc nhất #*(LessonName)*#
Bài học về hàm số có dạng y = ax + b với a ≠ 0 #*(LessonDescription)*#
18/07/2025 #*(CreatedDate)*#
=== SLIDE 1 SUMMARY ===
Placeholders: 1xLessonName, 1xLessonDescription, 1xCreatedDate
===========================
"""

        if config_prompt:
            prompt += f"\nADDITIONAL REQUIREMENTS:\n{config_prompt}\n"

        prompt += "\nHãy tạo nội dung theo format trên:"

        return prompt

    def _detect_placeholder_type_from_text(self, text: str, placeholder_patterns: Dict[str, str]) -> Optional[tuple]:
        """
        Detect placeholder type và max_length từ text format "PlaceholderName max_length"

        Args:
            text: Text từ element
            placeholder_patterns: Dictionary của patterns

        Returns:
            tuple: (placeholder_type, max_length) hoặc None nếu không detect được
        """
        try:
            for placeholder_type, pattern in placeholder_patterns.items():
                match = re.search(pattern, text)
                if match:
                    max_length = int(match.group(1))
                    return placeholder_type, max_length

            return None

        except Exception as e:
            logger.warning(f"Error detecting placeholder type: {e}")
            return None

    def _generate_slide_description(self, placeholder_counts: Dict[str, int]) -> str:
        """
        Generate description for slide based on placeholder counts (từ luồng cũ)

        Args:
            placeholder_counts: Dictionary of placeholder type counts

        Returns:
            str: Generated description
        """
        try:
            if not placeholder_counts:
                return "Slide trống"

            descriptions = []
            for placeholder_type, count in placeholder_counts.items():
                if count > 0:
                    if count == 1:
                        descriptions.append(f"1 {placeholder_type}")
                    else:
                        descriptions.append(f"{count} {placeholder_type}")

            if descriptions:
                return f"Slide dành cho {', '.join(descriptions)}"
            else:
                return "Slide trống"

        except Exception as e:
            logger.warning(f"Error generating slide description: {e}")
            return "Slide không xác định"

    async def _map_content_to_json_template(
        self,
        llm_content: str,
        original_template: Dict[str, Any],
        analyzed_template: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Map nội dung LLM vào JSON template theo logic của luồng cũ"""
        try:
            logger.info("🔧 Mapping LLM content to JSON template...")

            # Parse LLM content với slide summaries
            parsed_data = self._parse_llm_content(llm_content)
            slide_summaries = parsed_data.get("_slide_summaries", [])

            # Create processed template copy
            processed_template = {
                "version": original_template.get("version", "1.0"),
                "createdAt": datetime.now().isoformat(),
                "slideFormat": original_template.get("slideFormat", "16:9"),
                "slides": []
            }

            # Content index để track việc sử dụng content (như luồng cũ)
            content_index = {
                "LessonName": 0,
                "LessonDescription": 0,
                "CreatedDate": 0,
                "TitleName": 0,
                "TitleContent": 0,
                "SubtitleName": 0,
                "SubtitleContent": 0,
                "ImageName": 0,
                "ImageContent": 0
            }

            # Process từng slide theo analyzed template
            for i, analyzed_slide in enumerate(analyzed_template["slides"]):
                original_slide = analyzed_slide.get("original_slide")
                if not original_slide:
                    continue

                logger.info(f"🔍 Processing slide {i+1}: {analyzed_slide.get('description', 'Unknown')}")

                processed_slide = {
                    "id": original_slide.get("id"),
                    "title": original_slide.get("title", ""),
                    "elements": [],
                    "isVisible": original_slide.get("isVisible", True),
                    "background": original_slide.get("background", "#ffffff")
                }

                # Process từng analyzed element
                for analyzed_element in analyzed_slide.get("elements", []):
                    original_element = analyzed_element.get("original_element")
                    if not original_element:
                        continue

                    processed_element = {
                        "id": original_element.get("id"),
                        "type": original_element.get("type", "text"),
                        "x": original_element.get("x", 0),
                        "y": original_element.get("y", 0),
                        "width": original_element.get("width", 0),
                        "height": original_element.get("height", 0),
                        "text": original_element.get("text", ""),  # Default to original
                        "style": original_element.get("style", {})
                    }

                    # Map content từ parsed data
                    placeholder_type = analyzed_element.get("Type")
                    max_length = analyzed_element.get("max_length", 1000)

                    if placeholder_type:
                        content_list = parsed_data.get(placeholder_type, [])
                        current_index = content_index.get(placeholder_type, 0)

                        if current_index < len(content_list):
                            content_item = content_list[current_index]
                            raw_content = content_item.get("content", "")

                            # Handle max_length
                            final_content = await self._handle_max_length_content(
                                raw_content, max_length, placeholder_type
                            )

                            processed_element["text"] = final_content
                            content_index[placeholder_type] = current_index + 1

                            logger.info(f"✅ Mapped {placeholder_type} content: {len(final_content)} chars")
                        else:
                            logger.warning(f"⚠️ No more content for {placeholder_type}, keeping original text")

                    processed_slide["elements"].append(processed_element)

                processed_template["slides"].append(processed_slide)

            logger.info(f"✅ Template processing complete: {len(processed_template['slides'])} slides")
            return processed_template

        except Exception as e:
            logger.error(f"❌ Error mapping content to template: {e}")
            raise

    def _parse_llm_content(self, llm_content: str) -> Dict[str, List[Dict[str, Any]]]:
        """Parse nội dung từ LLM theo format của luồng cũ với slide summaries"""
        try:
            logger.info("📝 Parsing LLM content with slide summaries...")

            parsed_data = {
                "LessonName": [],
                "LessonDescription": [],
                "CreatedDate": [],
                "TitleName": [],
                "TitleContent": [],
                "SubtitleName": [],
                "SubtitleContent": [],
                "ImageName": [],
                "ImageContent": []
            }

            # Parse content theo annotation format
            valid_placeholders = '|'.join(parsed_data.keys())
            pattern = rf'#\*({valid_placeholders})\*#\s*(.*?)(?=#\*(?:{valid_placeholders})\*#|===|\n|$)'

            matches = re.findall(pattern, llm_content, re.DOTALL | re.IGNORECASE)

            logger.info(f"🔍 Found {len(matches)} annotation matches")

            for placeholder_type, content in matches:
                clean_content = content.strip()
                if clean_content:
                    parsed_data[placeholder_type].append({
                        "content": clean_content,
                        "length": len(clean_content)
                    })
                    logger.info(f"✅ Parsed {placeholder_type}: {clean_content[:50]}...")

            # Parse slide summaries để hiểu cấu trúc (như luồng cũ)
            slide_summaries = []
            summary_pattern = r'=== SLIDE (\d+) SUMMARY ===\s*Placeholders:\s*([^=]+)'
            summary_matches = re.findall(summary_pattern, llm_content, re.IGNORECASE)

            for slide_num_str, placeholder_text in summary_matches:
                slide_num = int(slide_num_str)
                placeholders = []
                placeholder_counts = {}

                # Parse placeholder counts từ text như "1xLessonName, 2xTitleContent"
                for item in placeholder_text.split(','):
                    item = item.strip()
                    if 'x' in item:
                        # Format: "2xTitleName"
                        count_str, placeholder_type = item.split('x', 1)
                        try:
                            count = int(count_str)
                            placeholders.append(placeholder_type.strip())
                            placeholder_counts[placeholder_type.strip()] = count
                        except ValueError:
                            # Fallback nếu không parse được số
                            placeholders.append(item)
                            placeholder_counts[item] = 1
                    else:
                        # Format cũ: "TitleName"
                        placeholders.append(item)
                        placeholder_counts[item] = 1

                slide_summaries.append({
                    "slide_number": slide_num,
                    "placeholders": placeholders,
                    "placeholder_counts": placeholder_counts
                })

            # Log parsed results
            logger.info(f"📋 Parsed {len(slide_summaries)} slide summaries")
            for placeholder_type, items in parsed_data.items():
                if items:
                    logger.info(f"📋 {placeholder_type}: {len(items)} items")

            # Store slide summaries for mapping logic
            parsed_data["_slide_summaries"] = slide_summaries

            return parsed_data

        except Exception as e:
            logger.error(f"❌ Error parsing LLM content: {e}")
            raise

    async def _handle_max_length_content(
        self,
        content: str,
        max_length: int,
        placeholder_type: str,
        max_retries: int = 3
    ) -> str:
        """Xử lý content vượt quá max_length"""
        try:
            if len(content) <= max_length:
                return content

            logger.info(f"⚠️ Content too long for {placeholder_type}: {len(content)} > {max_length}")

            # Retry với LLM để rút gọn
            for attempt in range(max_retries):
                logger.info(f"🔄 Retry {attempt + 1}/{max_retries} to shorten content...")

                shorten_prompt = f"""Hãy rút gọn nội dung sau để không vượt quá {max_length} ký tự, giữ nguyên ý nghĩa chính:

ORIGINAL CONTENT:
{content}

REQUIREMENTS:
- Tối đa {max_length} ký tự
- Giữ nguyên ý nghĩa chính
- Phù hợp với {placeholder_type}

SHORTENED CONTENT:"""

                llm_response = await self.llm_service.generate_content(
                    prompt=shorten_prompt,
                    max_tokens=max_length // 2,
                    temperature=0.2
                )

                if llm_response.get("success", False):
                    shortened_content = llm_response.get("text", "").strip()
                    if len(shortened_content) <= max_length:
                        logger.info(f"✅ Content shortened: {len(shortened_content)} chars")
                        return shortened_content

            # Không sử dụng fallback truncation
            logger.error(f"❌ Failed to shorten content for {placeholder_type} after {max_retries} retries")
            return content  # Trả về content gốc, để frontend xử lý

        except Exception as e:
            logger.error(f"❌ Error handling max_length content: {e}")
            return content  # Trả về content gốc, không truncate


# Singleton instance
_json_template_service = None

def get_json_template_service() -> JsonTemplateService:
    """Get singleton instance của JsonTemplateService"""
    global _json_template_service
    if _json_template_service is None:
        _json_template_service = JsonTemplateService()
    return _json_template_service
