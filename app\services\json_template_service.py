"""
JSON Template Processing Service
Xử lý slide generation với JSON template từ frontend thay vì Google Slides
"""

import logging
import re
from typing import Dict, List, Any, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.qdrant_service import get_qdrant_service

logger = logging.getLogger(__name__)


class JsonTemplateService:
    """Service xử lý JSON template từ frontend"""
    
    def __init__(self):
        self.llm_service = get_llm_service()
        self.qdrant_service = get_qdrant_service()
        
    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        return (
            self.llm_service and self.llm_service.is_available() and
            self.qdrant_service and self.qdrant_service.is_available()
        )
    
    async def process_json_template(
        self,
        lesson_id: str,
        template_json: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Xử lý JSON template với nội dung bài học
        
        Args:
            lesson_id: ID của bài học
            template_json: JSON template từ frontend
            config_prompt: Prompt cấu hình tùy chỉnh
            
        Returns:
            Dict chứa template đã được xử lý
        """
        try:
            logger.info(f"🔄 Processing JSON template for lesson: {lesson_id}")
            
            # Bước 1: Lấy nội dung bài học
            lesson_content = await self._get_lesson_content(lesson_id)
            if not lesson_content["success"]:
                return lesson_content
                
            # Bước 2: Phân tích template và detect placeholders
            analyzed_template = self._analyze_json_template(template_json)
            logger.info(f"📊 Analyzed template: {len(analyzed_template['slides'])} slides")
            
            # Bước 3: Sinh nội dung với LLM
            presentation_content = await self._generate_presentation_content(
                lesson_content["content"],
                analyzed_template,
                config_prompt
            )
            if not presentation_content["success"]:
                return presentation_content
                
            # Bước 4: Map nội dung vào template
            processed_template = await self._map_content_to_json_template(
                presentation_content["content"],
                template_json,
                analyzed_template
            )
            
            return {
                "success": True,
                "lesson_id": lesson_id,
                "processed_template": processed_template,
                "slides_created": len(processed_template.get("slides", [])),
                "presentation_content": presentation_content["content"]  # For debugging
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing JSON template: {e}")
            return {
                "success": False,
                "error": f"Failed to process JSON template: {str(e)}"
            }
    
    async def _get_lesson_content(self, lesson_id: str) -> Dict[str, Any]:
        """Lấy nội dung bài học từ Qdrant"""
        try:
            logger.info(f"📚 Getting lesson content for: {lesson_id}")

            # Search trong Qdrant sử dụng global_search
            search_results = await self.qdrant_service.global_search(
                query=lesson_id,
                limit=10,
                lesson_id=lesson_id
            )

            if not search_results.get("success", False) or not search_results.get("results"):
                return {
                    "success": False,
                    "error": f"No lesson content found for lesson_id: {lesson_id}"
                }

            # Combine content từ search results
            combined_content = ""
            results = search_results.get("results", [])

            for result in results:
                if "payload" in result and "content" in result["payload"]:
                    combined_content += result["payload"]["content"] + "\n\n"

            if not combined_content.strip():
                return {
                    "success": False,
                    "error": f"Empty lesson content for lesson_id: {lesson_id}"
                }

            logger.info(f"✅ Retrieved lesson content: {len(combined_content)} characters")
            return {
                "success": True,
                "content": combined_content.strip()
            }

        except Exception as e:
            logger.error(f"❌ Error getting lesson content: {e}")
            return {
                "success": False,
                "error": f"Failed to get lesson content: {str(e)}"
            }
    
    def _analyze_json_template(self, template_json: Dict[str, Any]) -> Dict[str, Any]:
        """Phân tích JSON template và detect placeholders"""
        try:
            logger.info("🔍 Analyzing JSON template structure...")
            
            slides = template_json.get("slides", [])
            analyzed_slides = []
            
            # Placeholder patterns để detect
            placeholder_patterns = {
                "LessonName": r"LessonName\s+(\d+)",
                "LessonDescription": r"LessonDescription\s+(\d+)", 
                "CreatedDate": r"CreatedDate\s+(\d+)",
                "TitleName": r"TitleName\s+(\d+)",
                "TitleContent": r"TitleContent\s+(\d+)",
                "SubtitleName": r"SubtitleName\s+(\d+)",
                "SubtitleContent": r"SubtitleContent\s+(\d+)",
                "ImageName": r"ImageName\s+(\d+)",
                "ImageContent": r"ImageContent\s+(\d+)"
            }
            
            for slide in slides:
                slide_info = {
                    "slideId": slide.get("id"),
                    "title": slide.get("title", ""),
                    "elements": [],
                    "placeholders": [],
                    "placeholder_counts": {}
                }
                
                # Phân tích elements
                for element in slide.get("elements", []):
                    text = element.get("text", "")
                    element_info = {
                        "objectId": element.get("id"),
                        "text": text,
                        "Type": None,
                        "max_length": None,
                        "style": element.get("style", {}),
                        "position": {
                            "x": element.get("x", 0),
                            "y": element.get("y", 0),
                            "width": element.get("width", 0),
                            "height": element.get("height", 0)
                        }
                    }
                    
                    # Detect placeholder type và max_length
                    for placeholder_type, pattern in placeholder_patterns.items():
                        match = re.search(pattern, text)
                        if match:
                            max_length = int(match.group(1))
                            element_info["Type"] = placeholder_type
                            element_info["max_length"] = max_length
                            
                            # Track placeholders
                            if placeholder_type not in slide_info["placeholders"]:
                                slide_info["placeholders"].append(placeholder_type)
                            slide_info["placeholder_counts"][placeholder_type] = slide_info["placeholder_counts"].get(placeholder_type, 0) + 1
                            break
                    
                    slide_info["elements"].append(element_info)
                
                analyzed_slides.append(slide_info)
            
            result = {
                "slides": analyzed_slides,
                "total_slides": len(analyzed_slides),
                "slideFormat": template_json.get("slideFormat", "16:9"),
                "version": template_json.get("version", "1.0")
            }
            
            logger.info(f"✅ Template analysis complete: {len(analyzed_slides)} slides analyzed")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error analyzing JSON template: {e}")
            raise
    
    async def _generate_presentation_content(
        self,
        lesson_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """Sinh nội dung presentation với LLM"""
        try:
            logger.info("🤖 Generating presentation content with LLM...")
            
            # Tạo prompt cho LLM
            prompt = self._create_llm_prompt(lesson_content, analyzed_template, config_prompt)
            
            # Gọi LLM
            llm_response = await self.llm_service.generate_response(
                prompt=prompt,
                max_tokens=4000,
                temperature=0.7
            )
            
            if not llm_response.get("success", False):
                return {
                    "success": False,
                    "error": f"LLM generation failed: {llm_response.get('error', 'Unknown error')}"
                }
            
            content = llm_response.get("content", "")
            logger.info(f"✅ LLM content generated: {len(content)} characters")
            
            return {
                "success": True,
                "content": content
            }
            
        except Exception as e:
            logger.error(f"❌ Error generating presentation content: {e}")
            return {
                "success": False,
                "error": f"Failed to generate content: {str(e)}"
            }
    
    def _create_llm_prompt(
        self,
        lesson_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> str:
        """Tạo prompt cho LLM"""
        
        # Collect placeholder requirements
        all_placeholders = set()
        for slide in analyzed_template["slides"]:
            all_placeholders.update(slide["placeholders"])
        
        placeholder_descriptions = {
            "LessonName": "Tên bài học ngắn gọn",
            "LessonDescription": "Mô tả chi tiết về bài học",
            "CreatedDate": "Ngày tạo (định dạng dd/mm/yyyy)",
            "TitleName": "Tiêu đề chính của slide",
            "TitleContent": "Nội dung chi tiết của slide",
            "SubtitleName": "Tiêu đề phụ",
            "SubtitleContent": "Nội dung phụ",
            "ImageName": "Tên/mô tả hình ảnh",
            "ImageContent": "Mô tả chi tiết nội dung hình ảnh"
        }
        
        prompt = f"""Bạn là chuyên gia tạo nội dung slide giáo dục. Hãy tạo nội dung cho presentation dựa trên bài học sau:

LESSON CONTENT:
{lesson_content}

TEMPLATE REQUIREMENTS:
Template có {analyzed_template['total_slides']} slides với các placeholder types sau:
{', '.join(all_placeholders)}

INSTRUCTIONS:
1. Tạo nội dung cho từng placeholder type theo format: #*(PlaceholderType)*# [nội dung]
2. Đảm bảo nội dung phù hợp với độ dài yêu cầu trong template
3. Nội dung phải liên quan trực tiếp đến bài học
4. Sử dụng ngôn ngữ phù hợp với học sinh

PLACEHOLDER DESCRIPTIONS:
"""
        
        for placeholder_type in all_placeholders:
            if placeholder_type in placeholder_descriptions:
                prompt += f"- {placeholder_type}: {placeholder_descriptions[placeholder_type]}\n"
        
        if config_prompt:
            prompt += f"\nADDITIONAL REQUIREMENTS:\n{config_prompt}\n"
        
        prompt += "\nHãy tạo nội dung theo format yêu cầu:"
        
        return prompt

    async def _map_content_to_json_template(
        self,
        llm_content: str,
        original_template: Dict[str, Any],
        analyzed_template: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Map nội dung LLM vào JSON template"""
        try:
            logger.info("🔧 Mapping LLM content to JSON template...")

            # Parse LLM content
            parsed_data = self._parse_llm_content(llm_content)

            # Create processed template copy
            processed_template = {
                "version": original_template.get("version", "1.0"),
                "createdAt": datetime.now().isoformat(),
                "slideFormat": original_template.get("slideFormat", "16:9"),
                "slides": []
            }

            # Content index để track việc sử dụng content
            content_index = {placeholder_type: 0 for placeholder_type in parsed_data.keys()}

            # Process từng slide
            for i, original_slide in enumerate(original_template.get("slides", [])):
                analyzed_slide = analyzed_template["slides"][i] if i < len(analyzed_template["slides"]) else None

                if not analyzed_slide:
                    continue

                processed_slide = {
                    "id": original_slide.get("id"),
                    "title": original_slide.get("title", ""),
                    "elements": [],
                    "isVisible": original_slide.get("isVisible", True),
                    "background": original_slide.get("background", "#ffffff")
                }

                # Process từng element
                for j, original_element in enumerate(original_slide.get("elements", [])):
                    analyzed_element = analyzed_slide["elements"][j] if j < len(analyzed_slide["elements"]) else None

                    processed_element = {
                        "id": original_element.get("id"),
                        "type": original_element.get("type", "text"),
                        "x": original_element.get("x", 0),
                        "y": original_element.get("y", 0),
                        "width": original_element.get("width", 0),
                        "height": original_element.get("height", 0),
                        "text": original_element.get("text", ""),
                        "style": original_element.get("style", {})
                    }

                    # Map content nếu có placeholder
                    if analyzed_element and analyzed_element.get("Type"):
                        placeholder_type = analyzed_element["Type"]
                        max_length = analyzed_element.get("max_length", 1000)

                        # Get content for this placeholder
                        content_list = parsed_data.get(placeholder_type, [])
                        current_index = content_index.get(placeholder_type, 0)

                        if current_index < len(content_list):
                            content_item = content_list[current_index]
                            raw_content = content_item.get("content", "")

                            # Handle max_length
                            final_content = await self._handle_max_length_content(
                                raw_content, max_length, placeholder_type
                            )

                            processed_element["text"] = final_content
                            content_index[placeholder_type] = current_index + 1

                            logger.info(f"✅ Mapped {placeholder_type} content: {len(final_content)} chars")

                    processed_slide["elements"].append(processed_element)

                processed_template["slides"].append(processed_slide)

            logger.info(f"✅ Template processing complete: {len(processed_template['slides'])} slides")
            return processed_template

        except Exception as e:
            logger.error(f"❌ Error mapping content to template: {e}")
            raise

    def _parse_llm_content(self, llm_content: str) -> Dict[str, List[Dict[str, Any]]]:
        """Parse nội dung từ LLM theo format annotation"""
        try:
            logger.info("📝 Parsing LLM content...")

            parsed_data = {
                "LessonName": [],
                "LessonDescription": [],
                "CreatedDate": [],
                "TitleName": [],
                "TitleContent": [],
                "SubtitleName": [],
                "SubtitleContent": [],
                "ImageName": [],
                "ImageContent": []
            }

            # Pattern để tìm annotation: #*(PlaceholderType)*# content
            valid_placeholders = '|'.join(parsed_data.keys())
            pattern = rf'#\*({valid_placeholders})\*#\s*(.*?)(?=#\*(?:{valid_placeholders})\*#|$)'

            matches = re.findall(pattern, llm_content, re.DOTALL | re.IGNORECASE)

            for placeholder_type, content in matches:
                clean_content = content.strip()
                if clean_content:
                    parsed_data[placeholder_type].append({
                        "content": clean_content,
                        "length": len(clean_content)
                    })

            # Log parsed results
            for placeholder_type, items in parsed_data.items():
                if items:
                    logger.info(f"📋 {placeholder_type}: {len(items)} items")

            return parsed_data

        except Exception as e:
            logger.error(f"❌ Error parsing LLM content: {e}")
            raise

    async def _handle_max_length_content(
        self,
        content: str,
        max_length: int,
        placeholder_type: str,
        max_retries: int = 3
    ) -> str:
        """Xử lý content vượt quá max_length"""
        try:
            if len(content) <= max_length:
                return content

            logger.info(f"⚠️ Content too long for {placeholder_type}: {len(content)} > {max_length}")

            # Retry với LLM để rút gọn
            for attempt in range(max_retries):
                logger.info(f"🔄 Retry {attempt + 1}/{max_retries} to shorten content...")

                shorten_prompt = f"""Hãy rút gọn nội dung sau để không vượt quá {max_length} ký tự, giữ nguyên ý nghĩa chính:

ORIGINAL CONTENT:
{content}

REQUIREMENTS:
- Tối đa {max_length} ký tự
- Giữ nguyên ý nghĩa chính
- Phù hợp với {placeholder_type}

SHORTENED CONTENT:"""

                llm_response = await self.llm_service.generate_response(
                    prompt=shorten_prompt,
                    max_tokens=max_length // 2,
                    temperature=0.3
                )

                if llm_response.get("success", False):
                    shortened_content = llm_response.get("content", "").strip()
                    if len(shortened_content) <= max_length:
                        logger.info(f"✅ Content shortened: {len(shortened_content)} chars")
                        return shortened_content

            # Fallback: truncate
            logger.warning(f"⚠️ Using fallback truncation for {placeholder_type}")
            return content[:max_length-3] + "..."

        except Exception as e:
            logger.error(f"❌ Error handling max_length content: {e}")
            return content[:max_length-3] + "..." if len(content) > max_length else content


# Singleton instance
_json_template_service = None

def get_json_template_service() -> JsonTemplateService:
    """Get singleton instance của JsonTemplateService"""
    global _json_template_service
    if _json_template_service is None:
        _json_template_service = JsonTemplateService()
    return _json_template_service
